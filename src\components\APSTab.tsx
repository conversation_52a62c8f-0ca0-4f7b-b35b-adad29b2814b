
import React, { useState } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { BarChart3, TrendingUp, Users } from 'lucide-react';
import chartData from '../data/chartData.json';

const APSTab = () => {
  const apsData = chartData.aps;

  // Filter states
  const [fromDate, setFromDate] = useState<Date>();
  const [toDate, setToDate] = useState<Date>();

  // Column Chart Configuration for APS vs Calibrated
  const columnChartOptions = {
    chart: {
      type: 'column',
      height: 400,
      backgroundColor: 'transparent'
    },
    title: {
      text: 'Total No of APS vs APS Calibrated under MSI',
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#1f2937'
      }
    },
    xAxis: {
      categories: apsData.apsVsCalibrated.categories,
      title: {
        text: 'Regions',
        style: {
          color: '#6b7280',
          fontWeight: 'bold'
        }
      },
      labels: {
        style: {
          color: '#6b7280'
        },
        rotation: 0
      }
    },
    yAxis: {
      title: {
        text: 'Number of APS',
        style: {
          color: '#6b7280',
          fontWeight: 'bold'
        }
      },
      labels: {
        style: {
          color: '#6b7280'
        }
      },
      gridLineColor: '#e5e7eb'
    },
    legend: {
      align: 'center',
      verticalAlign: 'bottom',
      backgroundColor: 'transparent',
      itemStyle: {
        color: '#374151',
        fontWeight: 'bold'
      }
    },
    plotOptions: {
      column: {
        pointPadding: 0.2,
        borderWidth: 0,
        dataLabels: {
          enabled: true,
          style: {
            color: '#374151',
            fontWeight: 'bold'
          }
        }
      }
    },
    series: apsData.apsVsCalibrated.series,
    credits: {
      enabled: false
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#d1d5db',
      borderRadius: 8,
      shadow: true
    }
  };

  // Line Chart Configuration for MSI Scores Over Time
  const lineChartOptions = {
    chart: {
      type: 'line',
      height: 400,
      backgroundColor: 'transparent'
    },
    title: {
      text: 'MSI Scores Over Time',
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        color: '#1f2937'
      }
    },
    xAxis: {
      categories: apsData.msiScoresOverTime.categories,
      title: {
        text: 'Time Period',
        style: {
          color: '#6b7280',
          fontWeight: 'bold'
        }
      },
      labels: {
        style: {
          color: '#6b7280'
        },
        rotation: 0
      }
    },
    yAxis: {
      title: {
        text: 'MSI Score (%)',
        style: {
          color: '#6b7280',
          fontWeight: 'bold'
        }
      },
      labels: {
        style: {
          color: '#6b7280'
        }
      },
      gridLineColor: '#e5e7eb',
      min: 60,
      max: 100
    },
    legend: {
      align: 'center',
      verticalAlign: 'bottom',
      backgroundColor: 'transparent',
      itemStyle: {
        color: '#374151',
        fontWeight: 'bold'
      }
    },
    plotOptions: {
      line: {
        dataLabels: {
          enabled: true,
          style: {
            color: '#374151',
            fontWeight: 'bold'
          }
        },
        enableMouseTracking: true
      }
    },
    series: apsData.msiScoresOverTime.series,
    credits: {
      enabled: false
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#d1d5db',
      borderRadius: 8,
      shadow: true,
      formatter: function() {
        return `<b>${this.series.name}</b><br/>${this.x}: ${this.y}%`;
      }
    }
  };

  // Create stacked bar chart options for MSI Score categories
  const createStackedBarChartOptions = (title: string, data: any) => {
    const achievedData = data.series[0].data;
    const remainingData = data.series[1].data;
    const maxValues = data.maxValues;

    return {
      chart: {
        type: 'bar',
        height: 600,
        backgroundColor: 'transparent'
      },
      title: {
        text: title,
        style: {
          fontSize: '18px',
          fontWeight: 'bold',
          color: '#1f2937'
        }
      },
      xAxis: {
        categories: data.categories,
        title: {
          text: null
        },
        labels: {
          style: {
            color: '#6b7280',
            fontSize: '12px'
          }
        }
      },
      yAxis: {
        min: 0,
        max: 100,
        title: {
          text: 'Score',
          style: {
            color: '#6b7280',
            fontWeight: 'bold'
          }
        },
        labels: {
          style: {
            color: '#6b7280'
          }
        },
        gridLineColor: '#e5e7eb'
      },
      legend: {
        align: 'center',
        verticalAlign: 'bottom',
        backgroundColor: 'transparent',
        itemStyle: {
          color: '#374151',
          fontWeight: 'bold'
        }
      },
      plotOptions: {
        bar: {
          stacking: 'normal',
          dataLabels: {
            enabled: true,
            inside: true,
            style: {
              color: '#ffffff',
              fontWeight: 'bold',
              textOutline: 'none'
            },
            formatter: function() {
              return this.y > 5 ? this.y : '';
            }
          }
        }
      },
      series: [
        {
          name: 'Remaining',
          data: remainingData,
          color: '#e5e7eb',
          dataLabels: {
            enabled: false
          }
        },
        {
          name: 'Achieved',
          data: achievedData,
          color: '#10b981'
        }
      ],
      credits: {
        enabled: false
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#d1d5db',
        borderRadius: 8,
        shadow: true,
        formatter: function() {
          const maxValue = maxValues ? maxValues[this.point.index] : 100;
          return `<b>${this.series.name}</b><br/>${this.x}: ${this.y}<br/>Max: ${maxValue}`;
        }
      }
    };
  };

  // Create ESG Score stacked column chart options (vertical bars)
  const createESGStackedBarChartOptions = (title: string, data: any) => {
    const achievedData = data.series[0].data;
    const remainingData = data.series[0].data.map((achieved: number, index: number) =>
      data.series[1].data[index] - achieved
    );

    // ESG specific color mapping
    const esgColors = {
      'Environment': '#2c7c69',
      'Social': '#fc6e51',
      'Governance': '#4a90e2',
      'General': '#b0b0b0'
    };

    // Dull/muted versions of ESG colors for remaining values
    const esgDullColors = {
      'Environment': '#a8c5c0',
      'Social': '#feb5a5',
      'Governance': '#a4c4f1',
      'General': '#d8d8d8'
    };

    return {
      chart: {
        type: 'column',
        height: 400,
        backgroundColor: 'transparent'
      },
      title: {
        text: title,
        style: {
          fontSize: '18px',
          fontWeight: 'bold',
          color: '#1f2937'
        }
      },
      xAxis: {
        categories: data.categories,
        title: {
          text: 'ESG Categories',
          style: {
            color: '#6b7280',
            fontWeight: 'bold'
          }
        },
        labels: {
          style: {
            color: '#6b7280',
            fontSize: '12px'
          },
          rotation: 0
        }
      },
      yAxis: {
        min: 0,
        max: 100,
        title: {
          text: 'Score',
          style: {
            color: '#6b7280',
            fontWeight: 'bold'
          }
        },
        labels: {
          style: {
            color: '#6b7280'
          }
        },
        gridLineColor: '#e5e7eb'
      },
      legend: {
        align: 'center',
        verticalAlign: 'bottom',
        backgroundColor: 'transparent',
        itemStyle: {
          color: '#374151',
          fontWeight: 'bold'
        }
      },
      plotOptions: {
        column: {
          stacking: 'normal',
          dataLabels: {
            enabled: true,
            inside: true,
            style: {
              color: '#ffffff',
              fontWeight: 'bold',
              textOutline: 'none'
            },
            formatter: function() {
              return this.y > 5 ? this.y : '';
            }
          }
        }
      },
      series: [
        {
          name: 'Remaining',
          data: remainingData,
          colorByPoint: true,
          colors: data.categories.map((category: string) =>
            esgDullColors[category as keyof typeof esgDullColors] || '#e5e7eb'
          ),
          dataLabels: {
            enabled: false
          }
        },
        {
          name: 'Achieved',
          data: achievedData,
          colorByPoint: true,
          colors: data.categories.map((category: string) =>
            esgColors[category as keyof typeof esgColors] || '#10b981'
          ),
          dataLabels: {
            enabled: true,
            style: {
              color: '#ffffff',
              fontWeight: 'bold'
            },
            formatter: function() {
              return this.y;
            }
          }
        }
      ],
      credits: {
        enabled: false
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#d1d5db',
        borderRadius: 8,
        shadow: true,
        formatter: function() {
          return `<b>${this.series.name}</b><br/>${this.x}: ${this.y}<br/>Max: 100`;
        }
      }
    };
  };

  // Create pie chart options for MSI Grade
  const createPieChartOptions = (title: string, data: any) => {
    return {
      chart: {
        type: 'pie',
        height: 400,
        backgroundColor: 'transparent'
      },
      title: {
        text: title,
        style: {
          fontSize: '18px',
          fontWeight: 'bold',
          color: '#1f2937'
        }
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#d1d5db',
        borderRadius: 8,
        shadow: true,
        pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b><br/>Count: <b>{point.y}</b>'
      },
      accessibility: {
        point: {
          valueSuffix: '%'
        }
      },
      plotOptions: {
        pie: {
          allowPointSelect: true,
          cursor: 'pointer',
          dataLabels: {
            enabled: true,
            format: '<b>{point.name}</b>: {point.percentage:.1f} %',
            style: {
              color: '#374151',
              fontWeight: 'bold'
            }
          },
          showInLegend: true
        }
      },
      legend: {
        align: 'center',
        verticalAlign: 'bottom',
        backgroundColor: 'transparent',
        itemStyle: {
          color: '#374151',
          fontWeight: 'bold'
        }
      },
      series: [{
        name: 'APS Count',
        colorByPoint: true,
        data: data.data
      }],
      credits: {
        enabled: false
      }
    };
  };

  // Create management stacked column chart options
  const createManagementStackedColumnChartOptions = (title: string, data: any) => {
    const achievedData = data.series[0].data;
    const remainingData = data.series[0].data.map((achieved: number, index: number) =>
      data.series[1].data[index] - achieved
    );

    return {
      chart: {
        type: 'column',
        height: 400,
        backgroundColor: 'transparent'
      },
      title: {
        text: title,
        style: {
          fontSize: '16px',
          fontWeight: 'bold',
          color: '#1f2937'
        }
      },
      xAxis: {
        categories: data.categories,
        title: {
          text: 'Categories',
          style: {
            color: '#6b7280',
            fontWeight: 'bold'
          }
        },
        labels: {
          style: {
            color: '#6b7280',
            fontSize: '11px'
          },
          rotation: -45
        }
      },
      yAxis: {
        min: 0,
        max: 100,
        title: {
          text: 'Score',
          style: {
            color: '#6b7280',
            fontWeight: 'bold'
          }
        },
        labels: {
          style: {
            color: '#6b7280'
          }
        },
        gridLineColor: '#e5e7eb'
      },
      legend: {
        align: 'center',
        verticalAlign: 'bottom',
        backgroundColor: 'transparent',
        itemStyle: {
          color: '#374151',
          fontWeight: 'bold'
        }
      },
      plotOptions: {
        column: {
          stacking: 'normal',
          dataLabels: {
            enabled: true,
            inside: true,
            style: {
              color: '#ffffff',
              fontWeight: 'bold',
              textOutline: 'none'
            },
            formatter: function() {
              return this.y > 5 ? this.y : '';
            }
          }
        }
      },
      series: [
        {
          name: 'Remaining',
          data: remainingData,
          color: '#e5e7eb',
          dataLabels: {
            enabled: false
          }
        },
        {
          name: 'Achieved',
          data: achievedData,
          color: '#10b981'
        }
      ],
      credits: {
        enabled: false
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#d1d5db',
        borderRadius: 8,
        shadow: true,
        formatter: function() {
          return `<b>${this.series.name}</b><br/>${this.x}: ${this.y}<br/>Max: 100`;
        }
      }
    };
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium opacity-90 flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              APS Metrics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">408</div>
            <p className="text-orange-100 text-sm mt-1">Total APS across regions</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-red-500 to-red-600 text-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium opacity-90 flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Calibration Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">79.4%</div>
            <p className="text-red-100 text-sm mt-1">APS calibrated under MSI</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-indigo-500 to-indigo-600 text-white">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium opacity-90 flex items-center gap-2">
              <Users className="h-4 w-4" />
              Calibrated APS
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">328</div>
            <p className="text-indigo-100 text-sm mt-1">APS calibrated under MSI</p>
          </CardContent>
        </Card>
      </div>



      {/* Charts Section */}
      {/* Row 1: Total No of APS vs APS Calibrated under MSI Chart */}
      <Card className="shadow-lg">
        <CardContent className="p-6">
          <HighchartsReact
            highcharts={Highcharts}
            options={columnChartOptions}
          />
        </CardContent>
      </Card>

      {/* Row 2: MSI Scores Over Time Chart */}
      <Card className="shadow-lg">
        <CardContent className="p-6">
          <HighchartsReact
            highcharts={Highcharts}
            options={lineChartOptions}
          />
        </CardContent>
      </Card>

      {/* Advanced Filter Section */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-gray-900">Advanced Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4 items-end">
            {/* Zone Filter */}
            <div className="flex flex-col space-y-2 min-w-[150px]">
              <label className="text-sm font-medium text-gray-700">Zone:</label>
              <Select>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select Zone" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Zones</SelectItem>
                  <SelectItem value="east">East Zone</SelectItem>
                  <SelectItem value="north">North Zone</SelectItem>
                  <SelectItem value="south1">South1 Zone</SelectItem>
                  <SelectItem value="south2">South2 Zone</SelectItem>
                  <SelectItem value="west">West Zone</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* City Filter */}
            <div className="flex flex-col space-y-2 min-w-[150px]">
              <label className="text-sm font-medium text-gray-700">City:</label>
              <Select>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select City" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Cities</SelectItem>
                  <SelectItem value="mumbai">Mumbai</SelectItem>
                  <SelectItem value="delhi">Delhi</SelectItem>
                  <SelectItem value="bangalore">Bangalore</SelectItem>
                  <SelectItem value="chennai">Chennai</SelectItem>
                  <SelectItem value="kolkata">Kolkata</SelectItem>
                  <SelectItem value="pune">Pune</SelectItem>
                  <SelectItem value="hyderabad">Hyderabad</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* APS Name & Code Filter */}
            <div className="flex flex-col space-y-2 min-w-[200px]">
              <label className="text-sm font-medium text-gray-700">APS Name & Code:</label>
              <Select>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select APS" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All APS</SelectItem>
                  <SelectItem value="aps001">APS001 - Central Mumbai</SelectItem>
                  <SelectItem value="aps002">APS002 - South Delhi</SelectItem>
                  <SelectItem value="aps003">APS003 - East Bangalore</SelectItem>
                  <SelectItem value="aps004">APS004 - North Chennai</SelectItem>
                  <SelectItem value="aps005">APS005 - West Kolkata</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Area Office Filter */}
            <div className="flex flex-col space-y-2 min-w-[200px]">
              <label className="text-sm font-medium text-gray-700">Area Office:</label>
              <Select>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select Area Office" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Area Offices</SelectItem>
                  <SelectItem value="ao001">AO001 - Mumbai Area Office</SelectItem>
                  <SelectItem value="ao002">AO002 - Delhi Area Office</SelectItem>
                  <SelectItem value="ao003">AO003 - Bangalore Area Office</SelectItem>
                  <SelectItem value="ao004">AO004 - Chennai Area Office</SelectItem>
                  <SelectItem value="ao005">AO005 - Kolkata Area Office</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* From Date Filter */}
            <div className="flex flex-col space-y-2 min-w-[180px]">
              <label className="text-sm font-medium text-gray-700">From Date:</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !fromDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {fromDate ? format(fromDate, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={fromDate}
                    onSelect={setFromDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* To Date Filter */}
            <div className="flex flex-col space-y-2 min-w-[180px]">
              <label className="text-sm font-medium text-gray-700">To Date:</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !toDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {toDate ? format(toDate, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={toDate}
                    onSelect={setToDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Apply Filters Button */}
            <div className="flex flex-col space-y-2 min-w-[200px]">
              <label className="text-sm font-medium text-gray-700 opacity-0">Apply</label>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 h-10">
                Apply Advanced Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* MSI Score Categories Section - Single Chart */}
      <Card className="shadow-lg">
        <CardContent className="p-6">
          <HighchartsReact
            highcharts={Highcharts}
            options={createStackedBarChartOptions('MSI Score Categories', apsData.msiScoreCategories)}
          />
        </CardContent>
      </Card>

      {/* ESG Score and APS MSI Grade Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* ESG Score Chart */}
        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact
              highcharts={Highcharts}
              options={createESGStackedBarChartOptions('ESG Score', apsData.esgScore)}
            />
          </CardContent>
        </Card>

        {/* APS MSI Grade Pie Chart */}
        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact
              highcharts={Highcharts}
              options={createPieChartOptions('APS MSI Grade', apsData.msiGrade)}
            />
          </CardContent>
        </Card>
      </div>

      {/* Management Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Environment Management */}
        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact
              highcharts={Highcharts}
              options={createManagementStackedColumnChartOptions('Environment Management', apsData.environmentManagement)}
            />
          </CardContent>
        </Card>

        {/* Social Management */}
        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact
              highcharts={Highcharts}
              options={createManagementStackedColumnChartOptions('Social Management', apsData.socialManagement)}
            />
          </CardContent>
        </Card>

        {/* Governance Management */}
        <Card className="shadow-lg">
          <CardContent className="p-6">
            <HighchartsReact
              highcharts={Highcharts}
              options={createManagementStackedColumnChartOptions('Governance Management', apsData.governanceManagement)}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default APSTab;

